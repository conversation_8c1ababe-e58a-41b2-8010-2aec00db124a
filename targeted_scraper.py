#!/usr/bin/env python3
"""
针对性爬虫 - 专门针对发现的搜索页面
"""
import json
import time
import os
import re
import urllib.parse
from datetime import datetime

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class TargetedBookScraper:
    """针对性图书爬虫"""
    
    def __init__(self):
        self.base_url = "https://mpdc.capub.cn/"
        self.search_url = "https://mpdc.capub.cn/search.html#/quick?type=%E5%9B%BE%E4%B9%A6&search="
        self.books_data = []
        self.driver = None
        
        # 搜索关键词列表
        self.search_keywords = [
            "计算机", "数学", "物理", "化学", "英语", "中文", "历史", "地理",
            "经济", "管理", "法学", "教育", "心理", "哲学", "文学", "艺术",
            "医学", "工程", "建筑", "农业", "环境", "生物", "材料", "电子"
        ]
    
    def setup_selenium(self):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            return False
        
        try:
            chrome_options = Options()
            # 不使用无头模式，这样可以看到浏览器操作
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)
            print("✅ Selenium WebDriver设置成功")
            return True
            
        except Exception as e:
            print(f"❌ Selenium设置失败: {e}")
            return False
    
    def search_books_by_keyword(self, keyword):
        """根据关键词搜索图书"""
        print(f"正在搜索关键词: {keyword}")
        
        try:
            # 构建搜索URL
            encoded_keyword = urllib.parse.quote(keyword)
            search_url = f"{self.search_url}{encoded_keyword}"
            
            print(f"访问搜索页面: {search_url}")
            self.driver.get(search_url)
            time.sleep(5)  # 等待页面加载
            
            # 等待搜索结果加载
            time.sleep(3)
            
            # 查找搜索结果
            book_elements = self.find_book_elements()
            
            if book_elements:
                print(f"找到 {len(book_elements)} 个搜索结果")
                return self.extract_books_from_elements(book_elements)
            else:
                print("未找到搜索结果")
                return []
                
        except Exception as e:
            print(f"搜索失败: {e}")
            return []
    
    def find_book_elements(self):
        """查找页面中的图书元素"""
        # 尝试多种可能的选择器
        selectors = [
            '.book-item',
            '.search-result-item',
            '.result-item',
            '.book-list-item',
            '.item',
            '[class*="book"]',
            '[class*="result"]',
            'li',
            'div[onclick]',
            'a[href*="detail"]'
        ]
        
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    return elements
            except:
                continue
        
        print("未找到图书元素")
        return []
    
    def extract_books_from_elements(self, elements):
        """从元素中提取图书信息"""
        books = []
        
        for i, element in enumerate(elements[:20]):  # 限制处理前20个结果
            try:
                book_info = self.extract_book_info_from_element(element)
                if book_info and book_info.get('title'):
                    books.append(book_info)
                    print(f"  ✅ 提取图书 {i+1}: {book_info['title']}")
                else:
                    print(f"  ❌ 跳过元素 {i+1}: 无有效信息")
            except Exception as e:
                print(f"  ❌ 提取元素 {i+1} 失败: {e}")
                continue
        
        return books
    
    def extract_book_info_from_element(self, element):
        """从单个元素提取图书信息"""
        book_info = {
            'title': '',
            'isbn': '',
            'publisher': '',
            'publish_date': '',
            'author': '',
            'keywords': '',
            'cip_number': '',
            'cover_image_url': '',
            'detail_url': '',
            'scraped_at': datetime.now().isoformat()
        }
        
        try:
            # 获取元素的文本内容
            element_text = element.text
            element_html = element.get_attribute('outerHTML')
            
            # 提取标题
            title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '[class*="title"]', 'a']
            for selector in title_selectors:
                try:
                    title_element = element.find_element(By.CSS_SELECTOR, selector)
                    title_text = title_element.text.strip()
                    if title_text and len(title_text) > 3:  # 标题应该有一定长度
                        book_info['title'] = title_text
                        break
                except:
                    continue
            
            # 如果没有找到标题，使用元素的文本
            if not book_info['title'] and element_text:
                lines = element_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and len(line) > 3 and not line.isdigit():
                        book_info['title'] = line
                        break
            
            # 提取ISBN
            isbn_pattern = r'ISBN[：:\s]*([0-9\-X]{10,17})'
            isbn_match = re.search(isbn_pattern, element_text, re.IGNORECASE)
            if isbn_match:
                book_info['isbn'] = isbn_match.group(1)
            
            # 提取出版社
            if '出版社' in element_text or '出版' in element_text:
                publisher_pattern = r'([^，,。\n]*出版[社]?[^，,。\n]*)'
                publisher_match = re.search(publisher_pattern, element_text)
                if publisher_match:
                    book_info['publisher'] = publisher_match.group(1).strip()
            
            # 提取作者
            author_patterns = [
                r'作者[：:\s]*([^，,。\n]+)',
                r'编著[：:\s]*([^，,。\n]+)',
                r'主编[：:\s]*([^，,。\n]+)'
            ]
            for pattern in author_patterns:
                author_match = re.search(pattern, element_text)
                if author_match:
                    book_info['author'] = author_match.group(1).strip()
                    break
            
            # 提取出版时间
            date_pattern = r'(\d{4}[-年]\d{1,2}[-月]?\d{0,2}日?)'
            date_match = re.search(date_pattern, element_text)
            if date_match:
                book_info['publish_date'] = date_match.group(1)
            
            # 查找详情页链接
            try:
                link_element = element.find_element(By.TAG_NAME, 'a')
                href = link_element.get_attribute('href')
                if href:
                    book_info['detail_url'] = href
            except:
                pass
            
            # 查找图片
            try:
                img_element = element.find_element(By.TAG_NAME, 'img')
                src = img_element.get_attribute('src')
                if src:
                    book_info['cover_image_url'] = src
            except:
                pass
            
            return book_info
            
        except Exception as e:
            print(f"提取图书信息失败: {e}")
            return None
    
    def save_data(self):
        """保存数据"""
        os.makedirs('output/data', exist_ok=True)
        
        # 保存为JSON
        json_file = 'output/data/targeted_books_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.books_data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {json_file}")
        
        # 保存为CSV
        csv_file = 'output/data/targeted_books_data.csv'
        if self.books_data:
            import csv
            with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=self.books_data[0].keys())
                writer.writeheader()
                writer.writerows(self.books_data)
            print(f"数据已保存到: {csv_file}")
    
    def run(self):
        """运行针对性爬虫"""
        print("=== 针对性图书爬虫开始运行 ===")
        
        if not self.setup_selenium():
            print("❌ 无法设置Selenium，程序退出")
            return
        
        try:
            # 对每个关键词进行搜索
            for keyword in self.search_keywords[:5]:  # 先测试前5个关键词
                try:
                    books = self.search_books_by_keyword(keyword)
                    self.books_data.extend(books)
                    
                    print(f"关键词 '{keyword}' 找到 {len(books)} 本图书")
                    time.sleep(2)  # 延迟避免请求过快
                    
                except KeyboardInterrupt:
                    print("用户中断，正在保存数据...")
                    break
                except Exception as e:
                    print(f"搜索关键词 '{keyword}' 失败: {e}")
                    continue
            
            # 去重
            unique_books = []
            seen_titles = set()
            for book in self.books_data:
                title = book.get('title', '')
                if title and title not in seen_titles:
                    seen_titles.add(title)
                    unique_books.append(book)
            
            self.books_data = unique_books
            
            # 保存数据
            if self.books_data:
                self.save_data()
                print(f"\n=== 爬取完成，共获取 {len(self.books_data)} 本图书信息 ===")
                
                # 显示统计信息
                print("\n📊 统计信息:")
                print(f"总图书数: {len(self.books_data)}")
                print(f"有ISBN的图书: {len([b for b in self.books_data if b.get('isbn')])}")
                print(f"有出版社的图书: {len([b for b in self.books_data if b.get('publisher')])}")
                print(f"有作者的图书: {len([b for b in self.books_data if b.get('author')])}")
                
                # 显示前几本图书
                print("\n📚 示例图书:")
                for i, book in enumerate(self.books_data[:3]):
                    print(f"{i+1}. {book.get('title', 'Unknown')}")
                    if book.get('author'):
                        print(f"   作者: {book['author']}")
                    if book.get('publisher'):
                        print(f"   出版社: {book['publisher']}")
                    print()
            else:
                print("❌ 未获取到任何图书信息")
                
        except Exception as e:
            print(f"程序执行出错: {e}")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    scraper = TargetedBookScraper()
    scraper.run()

if __name__ == "__main__":
    main()
