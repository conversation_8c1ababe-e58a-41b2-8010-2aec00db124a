"""
数据处理和保存模块
"""
import json
import csv
import pandas as pd
import logging
from datetime import datetime
import config

class DataProcessor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.books_data = []
    
    def add_book(self, book_info):
        """添加图书信息"""
        if book_info and book_info.get('title'):
            # 添加抓取时间戳
            book_info['scraped_at'] = datetime.now().isoformat()
            self.books_data.append(book_info)
            self.logger.info(f"添加图书: {book_info['title']}")
    
    def save_to_csv(self, filename=None):
        """保存为CSV格式"""
        if not filename:
            filename = config.CSV_FILE
        
        try:
            if not self.books_data:
                self.logger.warning("没有数据可保存")
                return False
            
            df = pd.DataFrame(self.books_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"数据已保存到CSV文件: {filename}")
            return True
        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
            return False
    
    def save_to_json(self, filename=None):
        """保存为JSON格式"""
        if not filename:
            filename = config.JSON_FILE
        
        try:
            if not self.books_data:
                self.logger.warning("没有数据可保存")
                return False
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.books_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已保存到JSON文件: {filename}")
            return True
        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {e}")
            return False
    
    def save_all(self):
        """保存所有格式"""
        csv_success = self.save_to_csv()
        json_success = self.save_to_json()
        return csv_success and json_success
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.books_data:
            return {}
        
        stats = {
            'total_books': len(self.books_data),
            'books_with_isbn': len([b for b in self.books_data if b.get('isbn')]),
            'books_with_cover': len([b for b in self.books_data if b.get('cover_image_path')]),
            'unique_publishers': len(set(b.get('publisher', '') for b in self.books_data if b.get('publisher'))),
            'unique_authors': len(set(b.get('author', '') for b in self.books_data if b.get('author')))
        }
        
        return stats
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        if stats:
            print("\n=== 爬取统计信息 ===")
            print(f"总图书数量: {stats['total_books']}")
            print(f"有ISBN的图书: {stats['books_with_isbn']}")
            print(f"有封面图片的图书: {stats['books_with_cover']}")
            print(f"不同出版社数量: {stats['unique_publishers']}")
            print(f"不同作者数量: {stats['unique_authors']}")
            print("==================")
    
    def validate_data(self):
        """验证数据质量"""
        issues = []
        
        for i, book in enumerate(self.books_data):
            if not book.get('title'):
                issues.append(f"第{i+1}本书缺少标题")
            
            if not book.get('isbn'):
                issues.append(f"第{i+1}本书缺少ISBN: {book.get('title', 'Unknown')}")
            
            if not book.get('publisher'):
                issues.append(f"第{i+1}本书缺少出版社: {book.get('title', 'Unknown')}")
        
        if issues:
            self.logger.warning(f"数据质量问题: {len(issues)}个")
            for issue in issues[:10]:  # 只显示前10个问题
                self.logger.warning(issue)
        
        return len(issues) == 0
    
    def remove_duplicates(self):
        """移除重复数据"""
        original_count = len(self.books_data)
        
        # 基于ISBN去重
        seen_isbns = set()
        unique_books = []
        
        for book in self.books_data:
            isbn = book.get('isbn', '')
            if isbn and isbn not in seen_isbns:
                seen_isbns.add(isbn)
                unique_books.append(book)
            elif not isbn:
                # 对于没有ISBN的书，基于标题去重
                title = book.get('title', '')
                if title not in [b.get('title', '') for b in unique_books]:
                    unique_books.append(book)
        
        self.books_data = unique_books
        removed_count = original_count - len(self.books_data)
        
        if removed_count > 0:
            self.logger.info(f"移除了 {removed_count} 本重复图书")
        
        return removed_count
