"""
工具函数
"""
import os
import re
import time
import logging
import requests
from urllib.parse import urljoin, urlparse
from PIL import Image
import config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def clean_text(text):
    """清理文本"""
    if not text:
        return ""
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除特殊字符
    text = re.sub(r'[^\w\s\-\.\(\)\[\]（）【】：:，,。、/\\]', '', text)
    return text

def is_valid_isbn(isbn):
    """验证ISBN格式"""
    if not isbn:
        return False
    # 移除连字符和空格
    isbn = re.sub(r'[\s\-]', '', isbn)
    # 检查ISBN-10或ISBN-13格式
    return bool(re.match(r'^(97[89])?\d{9}[\dX]$', isbn, re.IGNORECASE))

def download_image(url, filename, max_size=(800, 800)):
    """下载并处理图片"""
    try:
        response = requests.get(url, headers=config.HEADERS, timeout=config.TIMEOUT)
        response.raise_for_status()
        
        # 保存原始图片
        filepath = os.path.join(config.IMAGES_DIR, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        # 压缩图片
        try:
            with Image.open(filepath) as img:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                img.save(filepath, optimize=True, quality=85)
        except Exception as e:
            logging.warning(f"图片压缩失败: {e}")
        
        return filepath
    except Exception as e:
        logging.error(f"下载图片失败 {url}: {e}")
        return None

def safe_filename(filename):
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 限制长度
    if len(filename) > 100:
        name, ext = os.path.splitext(filename)
        filename = name[:95] + ext
    return filename

def retry_request(func, max_retries=config.MAX_RETRIES, delay=config.REQUEST_DELAY):
    """重试装饰器"""
    def wrapper(*args, **kwargs):
        for attempt in range(max_retries):
            try:
                result = func(*args, **kwargs)
                if delay > 0:
                    time.sleep(delay)
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                logging.warning(f"请求失败，第{attempt + 1}次重试: {e}")
                time.sleep(delay * (attempt + 1))
        return None
    return wrapper
