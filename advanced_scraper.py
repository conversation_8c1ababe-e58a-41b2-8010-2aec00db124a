#!/usr/bin/env python3
"""
高级爬虫 - 处理JavaScript渲染的网站
"""
import json
import time
import os
import re
from datetime import datetime

# 尝试导入selenium，如果失败则使用备用方案
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️  Selenium未安装，将使用简化模式")

class AdvancedBookScraper:
    """高级图书爬虫"""
    
    def __init__(self):
        self.base_url = "https://mpdc.capub.cn/"
        self.books_data = []
        self.driver = None
        
        # 常见的图书网站URL模式
        self.common_patterns = [
            "/search",
            "/books",
            "/list",
            "/category",
            "/browse",
            "/catalog"
        ]
        
        # 可能的搜索参数
        self.search_params = [
            "?q=",
            "?search=",
            "?keyword=",
            "?type=book"
        ]
    
    def setup_selenium(self):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            return False
        
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 尝试自动下载ChromeDriver
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except:
                # 如果自动下载失败，尝试使用系统中的Chrome
                self.driver = webdriver.Chrome(options=chrome_options)
            
            self.driver.set_page_load_timeout(30)
            print("✅ Selenium WebDriver设置成功")
            return True
            
        except Exception as e:
            print(f"❌ Selenium设置失败: {e}")
            return False
    
    def explore_website_structure(self):
        """探索网站结构"""
        print("正在探索网站结构...")

        if not self.setup_selenium():
            print("无法使用Selenium，尝试其他方法...")
            return self.fallback_exploration()

        try:
            self.driver.get(self.base_url)
            time.sleep(8)  # 增加等待时间让JavaScript充分加载

            # 打印页面源码的一部分来调试
            page_source = self.driver.page_source
            print(f"页面源码长度: {len(page_source)}")

            # 查找所有链接（更广泛的搜索）
            all_links = self.driver.find_elements(By.TAG_NAME, 'a')
            found_links = []

            for element in all_links:
                try:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    if href and href.startswith('http'):
                        found_links.append({'url': href, 'text': text})
                except:
                    continue

            print(f"找到 {len(found_links)} 个链接")

            # 查找可能包含图书信息的链接
            book_related_links = []
            for link in found_links:
                url_lower = link['url'].lower()
                text_lower = link['text'].lower()

                # 更广泛的关键词匹配
                keywords = ['book', 'isbn', 'publish', 'press', 'catalog', 'search',
                           '图书', '教材', '出版', '搜索', '目录', '书籍', 'detail', 'list']

                if any(keyword in url_lower or keyword in text_lower for keyword in keywords):
                    book_related_links.append(link)
                    print(f"  📚 可能的图书相关链接: {link['text']} -> {link['url']}")

            # 尝试点击一些可能的链接
            if book_related_links:
                for link in book_related_links[:3]:  # 尝试前3个最有希望的链接
                    try:
                        print(f"正在探索: {link['url']}")
                        book_urls = self.explore_book_section(link['url'])
                        if book_urls:
                            return book_urls
                    except Exception as e:
                        print(f"探索链接失败: {e}")
                        continue

            # 尝试查找搜索功能
            return self.try_find_search_functionality()

        except Exception as e:
            print(f"探索网站结构失败: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def try_find_search_functionality(self):
        """尝试查找搜索功能"""
        print("尝试查找搜索功能...")

        # 更广泛的搜索框选择器
        search_selectors = [
            'input[type="search"]',
            'input[type="text"]',
            'input[name*="search"]',
            'input[name*="keyword"]',
            'input[name*="q"]',
            'input[placeholder*="搜索"]',
            'input[placeholder*="search"]',
            'input[placeholder*="查找"]',
            '.search input',
            '#search',
            '#searchInput',
            '.searchbox input'
        ]

        for selector in search_selectors:
            try:
                search_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if search_inputs:
                    print(f"✅ 找到搜索框: {selector}")
                    return self.try_search_books()
            except:
                continue

        print("❌ 未找到搜索框")
        return []
    
    def try_search_books(self):
        """尝试搜索图书"""
        print("尝试搜索图书...")
        
        search_terms = ['教材', '图书', '出版', 'book']
        book_urls = []
        
        for term in search_terms:
            try:
                # 查找搜索框
                search_input = self.driver.find_element(By.CSS_SELECTOR, 
                    'input[type="search"], input[name*="search"], .search input')
                
                # 清空并输入搜索词
                search_input.clear()
                search_input.send_keys(term)
                
                # 查找搜索按钮
                search_button = None
                button_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.search-btn',
                    '.search button'
                ]
                
                for selector in button_selectors:
                    try:
                        search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        break
                    except:
                        continue
                
                if search_button:
                    search_button.click()
                else:
                    # 如果没有找到按钮，尝试按回车
                    from selenium.webdriver.common.keys import Keys
                    search_input.send_keys(Keys.RETURN)
                
                # 等待搜索结果
                time.sleep(3)
                
                # 查找结果中的图书链接
                result_links = self.driver.find_elements(By.CSS_SELECTOR, 'a[href*="detail"], a[href*="book"]')
                for link in result_links:
                    href = link.get_attribute('href')
                    if href and href not in book_urls:
                        book_urls.append(href)
                
                print(f"搜索 '{term}' 找到 {len(result_links)} 个结果")
                
            except Exception as e:
                print(f"搜索 '{term}' 失败: {e}")
                continue
        
        return book_urls
    
    def explore_book_section(self, url):
        """探索图书版块"""
        print(f"探索图书版块: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # 查找图书链接
            book_link_selectors = [
                'a[href*="detail"]',
                'a[href*="book"]',
                'a[href*="isbn"]',
                '.book-item a',
                '.book-list a',
                '.item a'
            ]
            
            book_urls = []
            for selector in book_link_selectors:
                try:
                    links = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for link in links:
                        href = link.get_attribute('href')
                        if href and href not in book_urls:
                            book_urls.append(href)
                except:
                    continue
            
            return book_urls
            
        except Exception as e:
            print(f"探索图书版块失败: {e}")
            return []
    
    def fallback_exploration(self):
        """备用探索方法"""
        print("使用备用探索方法...")
        
        # 尝试常见的URL模式
        potential_urls = []
        
        for pattern in self.common_patterns:
            potential_urls.append(self.base_url.rstrip('/') + pattern)
        
        for param in self.search_params:
            potential_urls.append(self.base_url.rstrip('/') + '/search' + param + '图书')
            potential_urls.append(self.base_url.rstrip('/') + '/search' + param + 'book')
        
        print(f"尝试 {len(potential_urls)} 个可能的URL...")
        
        # 这里可以添加简单的HTTP请求来测试这些URL
        # 由于时间限制，暂时返回空列表
        return []
    
    def extract_book_info_selenium(self, url):
        """使用Selenium提取图书信息"""
        if not self.driver:
            if not self.setup_selenium():
                return None
        
        try:
            self.driver.get(url)
            time.sleep(3)
            
            book_info = {
                'url': url,
                'title': '',
                'isbn': '',
                'publisher': '',
                'publish_date': '',
                'author': '',
                'keywords': '',
                'cip_number': '',
                'cover_image_url': '',
                'scraped_at': datetime.now().isoformat()
            }
            
            # 提取标题
            title_selectors = ['h1', 'h2', '.title', '.book-title']
            for selector in title_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.text.strip():
                        book_info['title'] = element.text.strip()
                        break
                except:
                    continue
            
            # 提取其他信息
            page_text = self.driver.page_source
            
            # ISBN
            isbn_match = re.search(r'ISBN[：:\s]*([0-9\-X]{10,17})', page_text, re.IGNORECASE)
            if isbn_match:
                book_info['isbn'] = isbn_match.group(1)
            
            # 出版社
            publisher_match = re.search(r'出版社[：:\s]*([^<\n]+)', page_text)
            if publisher_match:
                book_info['publisher'] = publisher_match.group(1).strip()
            
            # 作者
            author_match = re.search(r'作者[：:\s]*([^<\n]+)', page_text)
            if author_match:
                book_info['author'] = author_match.group(1).strip()
            
            # 出版时间
            date_match = re.search(r'(\d{4}[-年]\d{1,2}[-月]?\d{0,2}日?)', page_text)
            if date_match:
                book_info['publish_date'] = date_match.group(1)
            
            # 封面图片
            try:
                img_element = self.driver.find_element(By.CSS_SELECTOR, 'img[src*="cover"], img[alt*="封面"]')
                book_info['cover_image_url'] = img_element.get_attribute('src')
            except:
                pass
            
            return book_info
            
        except Exception as e:
            print(f"提取图书信息失败: {e}")
            return None
    
    def save_data(self):
        """保存数据"""
        os.makedirs('output/data', exist_ok=True)
        
        # 保存为JSON
        json_file = 'output/data/advanced_books_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.books_data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {json_file}")
    
    def run(self):
        """运行高级爬虫"""
        print("=== 高级图书爬虫开始运行 ===")
        
        # 探索网站结构
        book_urls = self.explore_website_structure()
        
        if not book_urls:
            print("❌ 未找到图书页面")
            print("\n可能的原因:")
            print("1. 网站需要登录")
            print("2. 网站使用了复杂的JavaScript")
            print("3. 网站有反爬虫机制")
            print("4. URL结构与预期不同")
            return
        
        print(f"✅ 找到 {len(book_urls)} 个图书页面")
        
        # 提取图书信息
        for url in book_urls[:5]:  # 限制前5个进行测试
            book_info = self.extract_book_info_selenium(url)
            if book_info and book_info.get('title'):
                self.books_data.append(book_info)
                print(f"✅ 提取成功: {book_info['title']}")
            time.sleep(2)
        
        # 保存数据
        if self.books_data:
            self.save_data()
            print(f"\n=== 爬取完成，共获取 {len(self.books_data)} 本图书信息 ===")
        else:
            print("❌ 未获取到任何图书信息")

def main():
    """主函数"""
    scraper = AdvancedBookScraper()
    scraper.run()

if __name__ == "__main__":
    main()
