# 中国人民大学出版社教材信息平台爬虫 - 使用指南

## 快速开始

### 1. 环境检查
```bash
# 检查Python版本（需要3.8+）
python --version

# 检查网站可访问性
python test_website.py
```

### 2. 选择合适的爬虫版本

根据您的环境和需求选择：

#### 🚀 方案一：简化版爬虫（推荐新手）
**优点：** 无需复杂依赖，使用Python标准库
**适用：** 快速测试、学习爬虫原理

```bash
python simple_scraper.py
```

#### 🔧 方案二：完整版爬虫（推荐进阶）
**优点：** 功能完整，支持JavaScript渲染
**适用：** 生产环境、大规模爬取

```bash
# 安装依赖
python setup.py

# 运行爬虫
python main.py
```

#### ⚡ 方案三：高级爬虫（推荐专业）
**优点：** 智能探索网站结构，适应性强
**适用：** 复杂网站、动态内容

```bash
# 需要先安装selenium
pip install selenium webdriver-manager

# 运行高级爬虫
python advanced_scraper.py
```

## 详细使用说明

### 配置修改

编辑 `config.py` 文件来调整爬虫行为：

```python
# 请求延迟（秒）- 避免对服务器造成压力
REQUEST_DELAY = 1.0

# 请求超时时间
TIMEOUT = 30

# 最大重试次数
MAX_RETRIES = 3

# 是否使用无头模式（不显示浏览器窗口）
HEADLESS = True
```

### 输出文件说明

爬虫运行后会在 `output` 目录生成：

```
output/
├── data/
│   ├── books_data.csv          # CSV格式数据
│   ├── books_data.json         # JSON格式数据
│   └── advanced_books_data.json # 高级爬虫数据
├── images/                     # 图书封面图片
│   ├── 9787300123456_cover.jpg
│   └── ...
├── scraper.log                 # 运行日志
└── website_analysis.json       # 网站结构分析
```

### 数据字段说明

每本图书包含以下信息：

| 字段 | 说明 | 示例 |
|------|------|------|
| title | 图书标题 | "计算机科学导论" |
| isbn | ISBN编号 | "978-7-300-12345-6" |
| publisher | 出版社 | "中国人民大学出版社" |
| publish_date | 出版时间 | "2023年6月" |
| author | 作者 | "张三 编著" |
| keywords | 主题词 | "计算机,编程,教材" |
| cip_number | CIP核准号 | "2023-123456" |
| cover_image_url | 封面图片URL | "https://..." |
| cover_image_path | 本地图片路径 | "output/images/..." |
| url | 原始页面URL | "https://..." |
| scraped_at | 抓取时间 | "2024-01-01T12:00:00" |

## 常见问题解决

### Q1: 网站无法访问
```bash
# 检查网络连接
ping mpdc.capub.cn

# 测试网站访问
python test_website.py
```

### Q2: 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### Q3: Chrome浏览器问题
```bash
# 检查Chrome是否安装
google-chrome --version

# 手动下载ChromeDriver
# 访问: https://chromedriver.chromium.org/
```

### Q4: 内存不足
修改 `config.py`：
```python
CONCURRENT_REQUESTS = 1  # 减少并发
REQUEST_DELAY = 2.0      # 增加延迟
```

### Q5: 没有找到图书数据
可能原因：
1. 网站需要登录
2. 网站结构发生变化
3. 存在反爬虫机制

解决方案：
1. 检查网站是否需要注册登录
2. 更新爬虫代码适应新结构
3. 增加请求延迟和随机化

## 进阶技巧

### 1. 自定义搜索关键词
修改 `advanced_scraper.py` 中的搜索词：
```python
search_terms = ['教材', '图书', '计算机', '数学']  # 添加您感兴趣的关键词
```

### 2. 批量处理
```bash
# 处理多个关键词
for keyword in 计算机 数学 物理; do
    python simple_scraper.py --keyword $keyword
done
```

### 3. 数据分析
```python
import pandas as pd

# 读取数据
df = pd.read_csv('output/data/books_data.csv')

# 统计分析
print(f"总图书数: {len(df)}")
print(f"出版社分布:\n{df['publisher'].value_counts()}")
print(f"出版年份分布:\n{df['publish_date'].str[:4].value_counts()}")
```

## 法律和道德规范

### ⚠️ 重要提醒

1. **遵守robots.txt**: 检查网站的robots.txt文件
2. **合理频率**: 设置适当的请求延迟
3. **尊重版权**: 仅用于学习和研究目的
4. **数据使用**: 不得用于商业用途
5. **隐私保护**: 不抓取个人隐私信息

### 📋 使用建议

- 在非高峰时段运行爬虫
- 监控爬虫运行状态，及时停止异常行为
- 定期更新User-Agent和请求头
- 保存爬取日志以便问题排查

## 技术支持

如果遇到问题：

1. 查看 `output/scraper.log` 日志文件
2. 运行 `python test_website.py` 检查网站状态
3. 检查网络连接和防火墙设置
4. 更新依赖包到最新版本

## 更新日志

- v1.0: 基础爬虫功能
- v1.1: 添加Selenium支持
- v1.2: 增加智能网站探索
- v1.3: 优化错误处理和重试机制

---

**免责声明**: 本工具仅供学习和研究使用，使用者需遵守相关法律法规和网站使用条款。
