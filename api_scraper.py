#!/usr/bin/env python3
"""
API爬虫 - 基于发现的搜索API进行数据抓取
"""
import json
import time
import os
import re
import urllib.parse
import urllib.request
from datetime import datetime

class APIBookScraper:
    """基于API的图书爬虫"""
    
    def __init__(self):
        self.base_api_url = "https://mpdc.capub.cn/search.html#/quick"
        self.books_data = []
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://mpdc.capub.cn/',
        }
        
        # 搜索关键词
        self.search_keywords = [
            "计算机", "数学", "物理", "化学", "英语", "中文", "历史", "地理",
            "经济", "管理", "法学", "教育", "心理", "哲学", "文学", "艺术",
            "医学", "工程", "建筑", "农业", "环境", "生物", "材料", "电子",
            "机械", "电气", "土木", "软件", "网络", "数据库", "人工智能"
        ]
    
    def build_search_url(self, keyword="", page_num=1, page_size=20):
        """构建搜索URL"""
        params = {
            'type': '图书',
            'search': '',
            'pageNum': str(page_num),
            'pageSize': str(page_size),
            'keyword': keyword,
            'publishingStart': '',
            'publishingEnd': '',
            'applyStart': '',
            'applyEnd': '',
            'auditdateStart': '',
            'auditdateEnd': '',
            'cate': '',
            'ztc': '[]',
            'zz': '[]',
            'cbn': '',
            'publisher': '[]',
            'stat': '0',
            'download': '0',
            'ids': '',
            'searchType': 'book',
            'sortName': '',
            'sortType': '',
            'checkedTypeList': '["cip","marc"]',
            'checkedClass': '[]',
            'filterbox': '[]',
            'priceStart': '',
            'priceEnd': '',
            'createdateStart': '',
            'createdateEnd': '',
            'xglb': '[]',
            'xglbWord': '[]',
            'xglbAwardDate': '[]'
        }
        
        # 构建查询字符串
        query_string = urllib.parse.urlencode(params, quote_via=urllib.parse.quote)
        return f"{self.base_api_url}?{query_string}"
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            req = urllib.request.Request(url, headers=self.headers)
            with urllib.request.urlopen(req, timeout=30) as response:
                content = response.read()
                try:
                    return content.decode('utf-8')
                except UnicodeDecodeError:
                    return content.decode('gbk', errors='ignore')
        except Exception as e:
            print(f"获取页面失败: {e}")
            return None
    
    def search_with_selenium(self, keyword="", max_pages=5):
        """使用Selenium进行搜索"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager
            
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            books = []
            
            for page in range(1, max_pages + 1):
                try:
                    url = self.build_search_url(keyword, page, 20)
                    print(f"正在抓取第 {page} 页，关键词: {keyword}")
                    print(f"URL: {url}")
                    
                    driver.get(url)
                    time.sleep(5)  # 等待页面加载
                    
                    # 等待更长时间让JavaScript加载数据
                    time.sleep(3)
                    
                    # 获取页面源码
                    page_source = driver.page_source
                    
                    # 尝试从页面源码中提取数据
                    page_books = self.extract_books_from_html(page_source)
                    
                    if page_books:
                        books.extend(page_books)
                        print(f"第 {page} 页找到 {len(page_books)} 本图书")
                    else:
                        print(f"第 {page} 页未找到图书数据")
                        # 如果连续几页都没有数据，可能已经到了最后一页
                        if page > 1:
                            break
                    
                    time.sleep(2)  # 页面间延迟
                    
                except Exception as e:
                    print(f"抓取第 {page} 页失败: {e}")
                    continue
            
            driver.quit()
            return books
            
        except ImportError:
            print("❌ Selenium未安装，无法使用此方法")
            return []
        except Exception as e:
            print(f"Selenium搜索失败: {e}")
            return []
    
    def extract_books_from_html(self, html_content):
        """从HTML内容中提取图书信息"""
        books = []
        
        try:
            # 尝试查找JSON数据
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.__NUXT__\s*=\s*({.+?});',
                r'__INITIAL_STATE__\s*=\s*({.+?});',
                r'data\s*:\s*({.+?})',
                r'"books"\s*:\s*(\[.+?\])',
                r'"list"\s*:\s*(\[.+?\])',
                r'"data"\s*:\s*(\[.+?\])'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        extracted_books = self.extract_books_from_json(data)
                        if extracted_books:
                            books.extend(extracted_books)
                            print(f"从JSON数据中提取到 {len(extracted_books)} 本图书")
                    except:
                        continue
            
            # 如果没有找到JSON数据，尝试从HTML中提取
            if not books:
                books = self.extract_books_from_html_elements(html_content)
            
        except Exception as e:
            print(f"提取图书信息失败: {e}")
        
        return books
    
    def extract_books_from_json(self, data):
        """从JSON数据中提取图书信息"""
        books = []
        
        def find_book_arrays(obj, path=""):
            """递归查找可能包含图书信息的数组"""
            if isinstance(obj, dict):
                for key, value in obj.items():
                    new_path = f"{path}.{key}" if path else key
                    if key.lower() in ['books', 'list', 'data', 'items', 'results']:
                        if isinstance(value, list) and value:
                            # 检查数组中的第一个元素是否像图书信息
                            if self.looks_like_book_data(value[0]):
                                return value
                    result = find_book_arrays(value, new_path)
                    if result:
                        return result
            elif isinstance(obj, list):
                for item in obj:
                    if self.looks_like_book_data(item):
                        return obj
                    result = find_book_arrays(item, path)
                    if result:
                        return result
            return None
        
        book_array = find_book_arrays(data)
        if book_array:
            for item in book_array:
                book_info = self.parse_book_json(item)
                if book_info:
                    books.append(book_info)
        
        return books
    
    def looks_like_book_data(self, obj):
        """判断对象是否像图书数据"""
        if not isinstance(obj, dict):
            return False
        
        book_fields = ['title', 'isbn', 'author', 'publisher', 'name', 'bookname', 'book_name']
        return any(field in str(obj).lower() for field in book_fields)
    
    def parse_book_json(self, book_data):
        """解析单本图书的JSON数据"""
        if not isinstance(book_data, dict):
            return None
        
        book_info = {
            'title': '',
            'isbn': '',
            'publisher': '',
            'publish_date': '',
            'author': '',
            'keywords': '',
            'cip_number': '',
            'cover_image_url': '',
            'detail_url': '',
            'scraped_at': datetime.now().isoformat()
        }
        
        # 映射字段
        field_mappings = {
            'title': ['title', 'name', 'bookname', 'book_name', 'bookTitle', '书名'],
            'isbn': ['isbn', 'ISBN', 'book_isbn'],
            'publisher': ['publisher', 'press', 'publishing_house', '出版社'],
            'author': ['author', 'writer', 'authors', '作者', '编著'],
            'publish_date': ['publish_date', 'publication_date', 'date', '出版时间'],
            'cip_number': ['cip', 'cip_number', 'CIP'],
            'cover_image_url': ['cover', 'image', 'img', 'cover_url', 'image_url'],
            'detail_url': ['url', 'detail_url', 'link', 'href']
        }
        
        for field, possible_keys in field_mappings.items():
            for key in possible_keys:
                if key in book_data and book_data[key]:
                    book_info[field] = str(book_data[key]).strip()
                    break
        
        # 如果有标题，认为是有效的图书信息
        return book_info if book_info['title'] else None
    
    def extract_books_from_html_elements(self, html_content):
        """从HTML元素中提取图书信息"""
        books = []
        
        # 使用正则表达式查找可能的图书信息
        patterns = [
            r'<[^>]*class="[^"]*book[^"]*"[^>]*>(.*?)</[^>]*>',
            r'<[^>]*class="[^"]*item[^"]*"[^>]*>(.*?)</[^>]*>',
            r'<[^>]*class="[^"]*result[^"]*"[^>]*>(.*?)</[^>]*>'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                book_info = self.extract_book_from_html_snippet(match)
                if book_info:
                    books.append(book_info)
        
        return books
    
    def extract_book_from_html_snippet(self, html_snippet):
        """从HTML片段中提取图书信息"""
        book_info = {
            'title': '',
            'isbn': '',
            'publisher': '',
            'publish_date': '',
            'author': '',
            'keywords': '',
            'cip_number': '',
            'cover_image_url': '',
            'scraped_at': datetime.now().isoformat()
        }
        
        # 移除HTML标签，获取纯文本
        text = re.sub(r'<[^>]+>', ' ', html_snippet)
        text = re.sub(r'\s+', ' ', text).strip()
        
        if len(text) < 10:  # 太短的文本可能不是图书信息
            return None
        
        # 提取ISBN
        isbn_match = re.search(r'ISBN[：:\s]*([0-9\-X]{10,17})', text, re.IGNORECASE)
        if isbn_match:
            book_info['isbn'] = isbn_match.group(1)
        
        # 提取可能的标题（通常是最长的一段文字）
        words = text.split()
        if words:
            # 找最长的连续词组作为标题
            max_length = 0
            title = ""
            for i in range(len(words)):
                for j in range(i+1, min(i+10, len(words)+1)):
                    phrase = " ".join(words[i:j])
                    if len(phrase) > max_length and len(phrase) > 5:
                        max_length = len(phrase)
                        title = phrase
            book_info['title'] = title
        
        return book_info if book_info['title'] else None
    
    def save_data(self):
        """保存数据"""
        os.makedirs('output/data', exist_ok=True)
        
        # 保存为JSON
        json_file = 'output/data/api_books_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.books_data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {json_file}")
        
        # 保存为CSV
        if self.books_data:
            csv_file = 'output/data/api_books_data.csv'
            with open(csv_file, 'w', encoding='utf-8') as f:
                if self.books_data:
                    headers = list(self.books_data[0].keys())
                    f.write(','.join(headers) + '\n')
                    
                    for book in self.books_data:
                        row = []
                        for header in headers:
                            value = str(book.get(header, '')).replace(',', '，')
                            row.append(f'"{value}"')
                        f.write(','.join(row) + '\n')
            print(f"数据已保存到: {csv_file}")
    
    def run(self):
        """运行API爬虫"""
        print("=== API图书爬虫开始运行 ===")
        
        # 对每个关键词进行搜索
        for keyword in self.search_keywords[:3]:  # 先测试前3个关键词
            try:
                print(f"\n正在搜索关键词: {keyword}")
                books = self.search_with_selenium(keyword, max_pages=3)
                
                if books:
                    self.books_data.extend(books)
                    print(f"关键词 '{keyword}' 找到 {len(books)} 本图书")
                else:
                    print(f"关键词 '{keyword}' 未找到图书")
                
                time.sleep(3)  # 关键词间延迟
                
            except KeyboardInterrupt:
                print("用户中断，正在保存数据...")
                break
            except Exception as e:
                print(f"搜索关键词 '{keyword}' 失败: {e}")
                continue
        
        # 去重
        unique_books = []
        seen_titles = set()
        for book in self.books_data:
            title = book.get('title', '')
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_books.append(book)
        
        self.books_data = unique_books
        
        # 保存数据
        if self.books_data:
            self.save_data()
            print(f"\n=== 爬取完成，共获取 {len(self.books_data)} 本图书信息 ===")
            
            # 显示统计信息
            print("\n📊 统计信息:")
            print(f"总图书数: {len(self.books_data)}")
            print(f"有ISBN的图书: {len([b for b in self.books_data if b.get('isbn')])}")
            print(f"有出版社的图书: {len([b for b in self.books_data if b.get('publisher')])}")
            print(f"有作者的图书: {len([b for b in self.books_data if b.get('author')])}")
            
            # 显示前几本图书
            print("\n📚 示例图书:")
            for i, book in enumerate(self.books_data[:3]):
                print(f"{i+1}. {book.get('title', 'Unknown')}")
                if book.get('author'):
                    print(f"   作者: {book['author']}")
                if book.get('publisher'):
                    print(f"   出版社: {book['publisher']}")
                if book.get('isbn'):
                    print(f"   ISBN: {book['isbn']}")
                print()
        else:
            print("❌ 未获取到任何图书信息")

def main():
    """主函数"""
    scraper = APIBookScraper()
    scraper.run()

if __name__ == "__main__":
    main()
