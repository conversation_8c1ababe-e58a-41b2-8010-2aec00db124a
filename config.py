"""
配置文件
"""
import os

# 基础配置
BASE_URL = "https://mpdc.capub.cn/"
OUTPUT_DIR = "output"
IMAGES_DIR = os.path.join(OUTPUT_DIR, "images")
DATA_DIR = os.path.join(OUTPUT_DIR, "data")

# 爬虫配置
REQUEST_DELAY = 1.0  # 请求间隔（秒）
TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数
CONCURRENT_REQUESTS = 5  # 并发请求数

# 输出文件配置
CSV_FILE = os.path.join(DATA_DIR, "books_data.csv")
JSON_FILE = os.path.join(DATA_DIR, "books_data.json")

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Selenium配置
SELENIUM_TIMEOUT = 10
HEADLESS = True  # 是否使用无头模式

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = os.path.join(OUTPUT_DIR, "scraper.log")

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(IMAGES_DIR, exist_ok=True)
os.makedirs(DATA_DIR, exist_ok=True)
