"""
图书爬虫主类
"""
import re
import time
import logging
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import urljoin, urlparse
from fake_useragent import UserAgent
import config
from utils import clean_text, is_valid_isbn, download_image, safe_filename, retry_request

class BookScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
        self.logger = logging.getLogger(__name__)
        self.ua = UserAgent()
        self.driver = None
        
    def setup_selenium(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            if config.HEADLESS:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument(f'--user-agent={self.ua.random}')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(config.SELENIUM_TIMEOUT)
            return True
        except Exception as e:
            self.logger.error(f"Selenium设置失败: {e}")
            return False
    
    def close_selenium(self):
        """关闭Selenium WebDriver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    @retry_request
    def get_page(self, url, use_selenium=False):
        """获取页面内容"""
        try:
            if use_selenium:
                if not self.driver:
                    if not self.setup_selenium():
                        return None
                
                self.driver.get(url)
                WebDriverWait(self.driver, config.SELENIUM_TIMEOUT).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                return self.driver.page_source
            else:
                response = self.session.get(url, timeout=config.TIMEOUT)
                response.raise_for_status()
                response.encoding = response.apparent_encoding
                return response.text
        except Exception as e:
            self.logger.error(f"获取页面失败 {url}: {e}")
            return None
    
    def find_book_list_urls(self):
        """查找图书列表页面URL"""
        urls = []
        try:
            # 获取主页
            html = self.get_page(config.BASE_URL)
            if not html:
                return urls
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找可能的图书分类或列表链接
            # 这里需要根据实际网站结构调整选择器
            link_selectors = [
                'a[href*="book"]',
                'a[href*="list"]',
                'a[href*="category"]',
                'a[href*="search"]',
                '.nav a',
                '.menu a',
                '.category a'
            ]
            
            for selector in link_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href:
                        full_url = urljoin(config.BASE_URL, href)
                        if full_url not in urls:
                            urls.append(full_url)
            
            self.logger.info(f"找到 {len(urls)} 个可能的图书列表URL")
            return urls
            
        except Exception as e:
            self.logger.error(f"查找图书列表URL失败: {e}")
            return urls

    def extract_book_urls_from_list(self, list_url):
        """从列表页面提取图书详情页URL"""
        book_urls = []
        try:
            html = self.get_page(list_url, use_selenium=True)
            if not html:
                return book_urls

            soup = BeautifulSoup(html, 'html.parser')

            # 查找图书详情页链接
            book_link_selectors = [
                'a[href*="detail"]',
                'a[href*="book"]',
                'a[href*="isbn"]',
                '.book-item a',
                '.book-list a',
                '.item a'
            ]

            for selector in book_link_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href:
                        full_url = urljoin(config.BASE_URL, href)
                        if full_url not in book_urls:
                            book_urls.append(full_url)

            # 查找分页链接
            pagination_selectors = [
                'a[href*="page"]',
                '.pagination a',
                '.pager a',
                'a[href*="p="]'
            ]

            for selector in pagination_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href and ('next' in link.text.lower() or '下一页' in link.text):
                        next_url = urljoin(config.BASE_URL, href)
                        book_urls.extend(self.extract_book_urls_from_list(next_url))
                        break

            self.logger.info(f"从 {list_url} 提取到 {len(book_urls)} 个图书URL")
            return book_urls

        except Exception as e:
            self.logger.error(f"提取图书URL失败 {list_url}: {e}")
            return book_urls

    def extract_book_info(self, book_url):
        """提取单本图书的详细信息"""
        book_info = {
            'url': book_url,
            'title': '',
            'isbn': '',
            'publisher': '',
            'publish_date': '',
            'author': '',
            'keywords': '',
            'cip_number': '',
            'cover_image_url': '',
            'cover_image_path': ''
        }

        try:
            html = self.get_page(book_url, use_selenium=True)
            if not html:
                return book_info

            soup = BeautifulSoup(html, 'html.parser')

            # 提取书名
            title_selectors = [
                'h1',
                '.book-title',
                '.title',
                '[class*="title"]',
                'meta[property="og:title"]'
            ]

            for selector in title_selectors:
                element = soup.select_one(selector)
                if element:
                    if element.name == 'meta':
                        book_info['title'] = clean_text(element.get('content', ''))
                    else:
                        book_info['title'] = clean_text(element.get_text())
                    if book_info['title']:
                        break

            # 提取ISBN
            isbn_patterns = [
                r'ISBN[：:\s]*([0-9\-X]{10,17})',
                r'书号[：:\s]*([0-9\-X]{10,17})',
                r'([0-9]{13})',
                r'([0-9\-X]{10,17})'
            ]

            text_content = soup.get_text()
            for pattern in isbn_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    if is_valid_isbn(match):
                        book_info['isbn'] = clean_text(match)
                        break
                if book_info['isbn']:
                    break

            # 提取出版社
            publisher_selectors = [
                '[class*="publisher"]',
                '[class*="press"]',
                'td:contains("出版社")',
                'span:contains("出版社")'
            ]

            for selector in publisher_selectors:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text()
                    if '出版社' in text or '出版' in text:
                        book_info['publisher'] = clean_text(text)
                        break

            # 提取出版时间
            date_patterns = [
                r'出版时间[：:\s]*(\d{4}[-年]\d{1,2}[-月]?\d{0,2}日?)',
                r'出版日期[：:\s]*(\d{4}[-年]\d{1,2}[-月]?\d{0,2}日?)',
                r'(\d{4}[-年]\d{1,2}[-月]\d{1,2}日?)',
                r'(\d{4}年\d{1,2}月)',
                r'(\d{4}年)'
            ]

            for pattern in date_patterns:
                matches = re.findall(pattern, text_content)
                if matches:
                    book_info['publish_date'] = clean_text(matches[0])
                    break

            # 提取作者
            author_selectors = [
                '[class*="author"]',
                '[class*="writer"]',
                'td:contains("作者")',
                'span:contains("作者")'
            ]

            for selector in author_selectors:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text()
                    if '作者' in text or '编著' in text:
                        book_info['author'] = clean_text(text)
                        break

            # 提取主题词
            keywords_selectors = [
                '[class*="keyword"]',
                '[class*="tag"]',
                'meta[name="keywords"]',
                'td:contains("主题词")',
                'span:contains("主题词")'
            ]

            for selector in keywords_selectors:
                element = soup.select_one(selector)
                if element:
                    if element.name == 'meta':
                        book_info['keywords'] = clean_text(element.get('content', ''))
                    else:
                        book_info['keywords'] = clean_text(element.get_text())
                    if book_info['keywords']:
                        break

            # 提取CIP核准号
            cip_patterns = [
                r'CIP[核准号：:\s]*([0-9\-A-Z]+)',
                r'核准号[：:\s]*([0-9\-A-Z]+)',
                r'CIP\s*([0-9\-A-Z]+)'
            ]

            for pattern in cip_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    book_info['cip_number'] = clean_text(matches[0])
                    break

            # 提取封面图片
            image_selectors = [
                'img[src*="cover"]',
                'img[src*="book"]',
                '.book-cover img',
                '.cover img',
                'img[alt*="封面"]'
            ]

            for selector in image_selectors:
                img = soup.select_one(selector)
                if img:
                    src = img.get('src')
                    if src:
                        book_info['cover_image_url'] = urljoin(book_url, src)
                        break

            # 下载封面图片
            if book_info['cover_image_url'] and book_info['isbn']:
                filename = f"{safe_filename(book_info['isbn'])}_cover.jpg"
                image_path = download_image(book_info['cover_image_url'], filename)
                if image_path:
                    book_info['cover_image_path'] = image_path

            self.logger.info(f"成功提取图书信息: {book_info['title']}")
            return book_info

        except Exception as e:
            self.logger.error(f"提取图书信息失败 {book_url}: {e}")
            return book_info
