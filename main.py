"""
中国人民大学出版社教材信息平台爬虫主程序
"""
import time
import logging
from tqdm import tqdm
import config
from utils import setup_logging
from book_scraper import BookScraper
from data_processor import DataProcessor

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    logger.info("开始爬取中国人民大学出版社教材信息")
    
    # 初始化组件
    scraper = BookScraper()
    processor = DataProcessor()
    
    try:
        # 第一步：查找图书列表页面
        logger.info("正在查找图书列表页面...")
        list_urls = scraper.find_book_list_urls()
        
        if not list_urls:
            logger.error("未找到图书列表页面，请检查网站结构")
            return
        
        logger.info(f"找到 {len(list_urls)} 个列表页面")
        
        # 第二步：从列表页面提取图书详情页URL
        logger.info("正在提取图书详情页URL...")
        all_book_urls = []
        
        for list_url in tqdm(list_urls, desc="处理列表页面"):
            book_urls = scraper.extract_book_urls_from_list(list_url)
            all_book_urls.extend(book_urls)
            time.sleep(config.REQUEST_DELAY)
        
        # 去重
        all_book_urls = list(set(all_book_urls))
        logger.info(f"总共找到 {len(all_book_urls)} 个图书详情页")
        
        if not all_book_urls:
            logger.error("未找到图书详情页URL")
            return
        
        # 第三步：提取每本图书的详细信息
        logger.info("正在提取图书详细信息...")
        
        for i, book_url in enumerate(tqdm(all_book_urls, desc="提取图书信息")):
            try:
                book_info = scraper.extract_book_info(book_url)
                if book_info and book_info.get('title'):
                    processor.add_book(book_info)
                
                # 每处理10本书保存一次（防止数据丢失）
                if (i + 1) % 10 == 0:
                    processor.save_all()
                
                time.sleep(config.REQUEST_DELAY)
                
            except KeyboardInterrupt:
                logger.info("用户中断，正在保存已获取的数据...")
                break
            except Exception as e:
                logger.error(f"处理图书失败 {book_url}: {e}")
                continue
        
        # 第四步：数据处理和保存
        logger.info("正在处理和保存数据...")
        
        # 移除重复数据
        removed_count = processor.remove_duplicates()
        if removed_count > 0:
            logger.info(f"移除了 {removed_count} 本重复图书")
        
        # 验证数据质量
        processor.validate_data()
        
        # 保存最终数据
        if processor.save_all():
            logger.info("数据保存成功")
        else:
            logger.error("数据保存失败")
        
        # 显示统计信息
        processor.print_statistics()
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        # 清理资源
        scraper.close_selenium()
        logger.info("程序执行完成")

def test_single_book():
    """测试单本图书信息提取"""
    logger = setup_logging()
    scraper = BookScraper()
    processor = DataProcessor()
    
    # 这里可以放一个测试URL
    test_url = "https://mpdc.capub.cn/book/detail/123"  # 替换为实际的图书详情页URL
    
    try:
        logger.info(f"测试提取图书信息: {test_url}")
        book_info = scraper.extract_book_info(test_url)
        
        if book_info:
            processor.add_book(book_info)
            processor.save_all()
            processor.print_statistics()
            print("\n提取的图书信息:")
            for key, value in book_info.items():
                print(f"{key}: {value}")
        else:
            logger.error("未能提取到图书信息")
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        scraper.close_selenium()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_book()
    else:
        main()
